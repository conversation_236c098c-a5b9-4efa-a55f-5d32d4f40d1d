#include "CrashHandler.h"
#include <QStandardPaths>
#include <QTextStream>
#include <QFileInfo>
#include <QProcess>
#if QT_VERSION >= QT_VERSION_CHECK(6, 0, 0)
#include <QStringConverter>
#endif

// 静态成员变量初始化
QString CrashHandler::m_dumpDirectory;
QString CrashHandler::m_applicationName;
QString CrashHandler::m_applicationVersion;
bool CrashHandler::m_autoRestart = false;
QString CrashHandler::m_lastOperation;
CrashInfo CrashHandler::m_lastCrashInfo;

#ifdef Q_OS_WIN
LPTOP_LEVEL_EXCEPTION_FILTER CrashHandler::m_previousFilter = nullptr;
#endif

bool CrashHandler::install(const QString& dumpDir)
{
    qDebug() << "[CRASH_HANDLER] 安装崩溃处理器";
    
    // 设置dump目录
    if (dumpDir.isEmpty()) {
        m_dumpDirectory = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/CrashDumps";
    } else {
        m_dumpDirectory = dumpDir;
    }
    
    // 创建dump目录
    QDir dir;
    if (!dir.mkpath(m_dumpDirectory)) {
        qWarning() << "[CRASH_HANDLER] 无法创建dump目录:" << m_dumpDirectory;
        return false;
    }
    
    qDebug() << "[CRASH_HANDLER] Dump文件将保存到:" << m_dumpDirectory;
    
#ifdef Q_OS_WIN
    // 安装Windows异常处理器
    m_previousFilter = SetUnhandledExceptionFilter(unhandledExceptionFilter);
    if (m_previousFilter == nullptr) {
        qDebug() << "[CRASH_HANDLER] Windows异常处理器安装成功";
    } else {
        qDebug() << "[CRASH_HANDLER] 替换了之前的异常处理器";
    }
    
    // 清理旧的dump文件（保留最近10个）
    cleanupOldDumps();
    
    return true;
#else
    qWarning() << "[CRASH_HANDLER] 当前平台不支持崩溃处理器";
    return false;
#endif
}

void CrashHandler::uninstall()
{
    qDebug() << "[CRASH_HANDLER] 卸载崩溃处理器";
    
#ifdef Q_OS_WIN
    if (m_previousFilter != nullptr) {
        SetUnhandledExceptionFilter(m_previousFilter);
        m_previousFilter = nullptr;
    }
#endif
}

void CrashHandler::setDumpDirectory(const QString& dir)
{
    m_dumpDirectory = dir;
    QDir().mkpath(m_dumpDirectory);
}

void CrashHandler::setApplicationInfo(const QString& name, const QString& version)
{
    m_applicationName = name;
    m_applicationVersion = version;
    qDebug() << "[CRASH_HANDLER] 应用程序信息:" << name << "版本:" << version;
}

void CrashHandler::setAutoRestart(bool enable)
{
    m_autoRestart = enable;
    qDebug() << "[CRASH_HANDLER] 自动重启:" << (enable ? "启用" : "禁用");
}

void CrashHandler::recordLastOperation(const QString& operation)
{
    m_lastOperation = QString("[%1] %2").arg(QDateTime::currentDateTime().toString("hh:mm:ss.zzz")).arg(operation);
    // 可以考虑将操作记录到文件中，以便崩溃后能够读取
}

#ifdef Q_OS_WIN
LONG WINAPI CrashHandler::unhandledExceptionFilter(EXCEPTION_POINTERS* exceptionInfo)
{
    qDebug() << "[CRASH_HANDLER] 捕获到未处理异常!";
    
    try {
        // 收集崩溃信息
        CrashInfo crashInfo;
        crashInfo.crashTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        crashInfo.applicationName = m_applicationName;
        crashInfo.applicationVersion = m_applicationVersion;
        crashInfo.processId = QString::number(GetCurrentProcessId());
        crashInfo.threadId = QString::number(GetCurrentThreadId());
        crashInfo.lastOperation = m_lastOperation;
        
        // 格式化异常信息
        if (exceptionInfo && exceptionInfo->ExceptionRecord) {
            EXCEPTION_RECORD* record = exceptionInfo->ExceptionRecord;
            crashInfo.exceptionCode = formatExceptionCode(record->ExceptionCode);
            crashInfo.exceptionAddress = QString("0x%1").arg(
                reinterpret_cast<quintptr>(record->ExceptionAddress), 0, 16);
            crashInfo.moduleName = getModuleInfo(record->ExceptionAddress);
        }
        
        // 收集系统信息
        crashInfo.systemInfo = collectSystemInfo();
        crashInfo.memoryUsage = collectMemoryInfo();
        
        // 生成dump文件
        QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
        QString dumpFileName = QString("%1_crash_%2.dmp")
                              .arg(m_applicationName.isEmpty() ? "app" : m_applicationName)
                              .arg(timestamp);
        crashInfo.dumpFilePath = m_dumpDirectory + "/" + dumpFileName;
        
        bool dumpSuccess = writeDumpFile(crashInfo.dumpFilePath, exceptionInfo);
        qDebug() << "[CRASH_HANDLER] Dump文件生成:" << (dumpSuccess ? "成功" : "失败");
        
        // 生成崩溃报告
        QString reportFileName = QString("%1_crash_%2.txt")
                                .arg(m_applicationName.isEmpty() ? "app" : m_applicationName)
                                .arg(timestamp);
        crashInfo.reportFilePath = m_dumpDirectory + "/" + reportFileName;
        
        bool reportSuccess = writeCrashReport(crashInfo);
        qDebug() << "[CRASH_HANDLER] 崩溃报告生成:" << (reportSuccess ? "成功" : "失败");
        
        // 保存崩溃信息
        m_lastCrashInfo = crashInfo;
        
        // 显示崩溃信息（可选）
        QString message = QString("应用程序发生崩溃\n\n"
                                 "时间: %1\n"
                                 "异常: %2\n"
                                 "地址: %3\n"
                                 "模块: %4\n\n"
                                 "Dump文件: %5\n"
                                 "报告文件: %6")
                         .arg(crashInfo.crashTime)
                         .arg(crashInfo.exceptionCode)
                         .arg(crashInfo.exceptionAddress)
                         .arg(crashInfo.moduleName)
                         .arg(crashInfo.dumpFilePath)
                         .arg(crashInfo.reportFilePath);
        
        // 在Windows上显示消息框
        MessageBoxA(nullptr, message.toLocal8Bit().data(), "应用程序崩溃", MB_OK | MB_ICONERROR);
        
        // 自动重启（如果启用）
        if (m_autoRestart) {
            QString appPath = QCoreApplication::applicationFilePath();
            QProcess::startDetached(appPath);
        }
        
    } catch (...) {
        qDebug() << "[CRASH_HANDLER] 异常处理器中发生异常";
    }
    
    // 返回EXCEPTION_EXECUTE_HANDLER让系统终止程序
    return EXCEPTION_EXECUTE_HANDLER;
}
#endif

bool CrashHandler::writeDumpFile(const QString& filePath, void* exceptionInfo)
{
#ifdef Q_OS_WIN
    qDebug() << "[CRASH_HANDLER] 开始生成dump文件:" << filePath;

    HANDLE hFile = CreateFileA(filePath.toLocal8Bit().data(),
                              GENERIC_WRITE,
                              0,
                              nullptr,
                              CREATE_ALWAYS,
                              FILE_ATTRIBUTE_NORMAL,
                              nullptr);

    if (hFile == INVALID_HANDLE_VALUE) {
        qWarning() << "[CRASH_HANDLER] 无法创建dump文件:" << GetLastError();
        return false;
    }

    MINIDUMP_EXCEPTION_INFORMATION mdei;
    MINIDUMP_EXCEPTION_INFORMATION* pMdei = nullptr;

    if (exceptionInfo) {
        mdei.ThreadId = GetCurrentThreadId();
        mdei.ExceptionPointers = static_cast<EXCEPTION_POINTERS*>(exceptionInfo);
        mdei.ClientPointers = FALSE;
        pMdei = &mdei;
    }

    // 设置dump类型（包含更多信息用于调试）
    MINIDUMP_TYPE dumpType = static_cast<MINIDUMP_TYPE>(
        MiniDumpNormal |
        MiniDumpWithDataSegs |
        MiniDumpWithHandleData |
        MiniDumpWithThreadInfo |
        MiniDumpWithProcessThreadData
    );

    BOOL result = MiniDumpWriteDump(GetCurrentProcess(),
                                   GetCurrentProcessId(),
                                   hFile,
                                   dumpType,
                                   pMdei,
                                   nullptr,
                                   nullptr);

    CloseHandle(hFile);

    if (result) {
        qDebug() << "[CRASH_HANDLER] Dump文件生成成功";
        return true;
    } else {
        qWarning() << "[CRASH_HANDLER] Dump文件生成失败:" << GetLastError();
        return false;
    }
#else
    Q_UNUSED(filePath)
    Q_UNUSED(exceptionInfo)
    return false;
#endif
}

bool CrashHandler::writeCrashReport(const CrashInfo& crashInfo)
{
    qDebug() << "[CRASH_HANDLER] 生成崩溃报告:" << crashInfo.reportFilePath;

    QFile reportFile(crashInfo.reportFilePath);
    if (!reportFile.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "[CRASH_HANDLER] 无法创建报告文件:" << crashInfo.reportFilePath;
        return false;
    }

    QTextStream out(&reportFile);
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    out.setCodec("UTF-8");
#else
    out.setEncoding(QStringConverter::Utf8);
#endif

    out << "==================== 应用程序崩溃报告 ====================" << "\n";
    out << "崩溃时间: " << crashInfo.crashTime << "\n";
    out << "应用程序: " << crashInfo.applicationName << "\n";
    out << "版本: " << crashInfo.applicationVersion << "\n";
    out << "进程ID: " << crashInfo.processId << "\n";
    out << "线程ID: " << crashInfo.threadId << "\n";
    out << "\n";

    out << "==================== 异常信息 ====================" << "\n";
    out << "异常代码: " << crashInfo.exceptionCode << "\n";
    out << "异常地址: " << crashInfo.exceptionAddress << "\n";
    out << "崩溃模块: " << crashInfo.moduleName << "\n";
    out << "\n";

    out << "==================== 最后操作 ====================" << "\n";
    out << crashInfo.lastOperation << "\n";
    out << "\n";

    out << "==================== 系统信息 ====================" << "\n";
    out << crashInfo.systemInfo << "\n";
    out << "\n";

    out << "==================== 内存使用 ====================" << "\n";
    out << crashInfo.memoryUsage << "\n";
    out << "\n";

    out << "==================== 文件信息 ====================" << "\n";
    out << "Dump文件: " << crashInfo.dumpFilePath << "\n";
    out << "报告文件: " << crashInfo.reportFilePath << "\n";
    out << "\n";

    out << "==================== 分析建议 ====================" << "\n";
    out << "1. 使用Visual Studio或WinDbg打开dump文件进行详细分析" << "\n";
    out << "2. 检查最后操作是否与崩溃相关" << "\n";
    out << "3. 查看内存使用情况是否存在内存泄漏" << "\n";
    out << "4. 检查崩溃模块是否为第三方库" << "\n";

    reportFile.close();
    qDebug() << "[CRASH_HANDLER] 崩溃报告生成完成";
    return true;
}

QString CrashHandler::collectSystemInfo()
{
    QString info;
    info += QString("操作系统: %1\n").arg(QSysInfo::prettyProductName());
    info += QString("内核类型: %1\n").arg(QSysInfo::kernelType());
    info += QString("内核版本: %1\n").arg(QSysInfo::kernelVersion());
    info += QString("CPU架构: %1\n").arg(QSysInfo::currentCpuArchitecture());
    info += QString("机器名称: %1\n").arg(QSysInfo::machineHostName());

#ifdef Q_OS_WIN
    SYSTEM_INFO sysInfo;
    GetSystemInfo(&sysInfo);
    info += QString("处理器数量: %1\n").arg(sysInfo.dwNumberOfProcessors);
    info += QString("页面大小: %1 bytes\n").arg(sysInfo.dwPageSize);

    MEMORYSTATUSEX memStatus;
    memStatus.dwLength = sizeof(memStatus);
    if (GlobalMemoryStatusEx(&memStatus)) {
        info += QString("物理内存总量: %1 MB\n").arg(memStatus.ullTotalPhys / 1024 / 1024);
        info += QString("可用物理内存: %1 MB\n").arg(memStatus.ullAvailPhys / 1024 / 1024);
        info += QString("内存使用率: %1%\n").arg(memStatus.dwMemoryLoad);
    }
#endif

    return info;
}

QString CrashHandler::collectMemoryInfo()
{
    QString info;

#ifdef Q_OS_WIN
    HANDLE hProcess = GetCurrentProcess();
    PROCESS_MEMORY_COUNTERS_EX pmc;

    if (GetProcessMemoryInfo(hProcess, (PROCESS_MEMORY_COUNTERS*)&pmc, sizeof(pmc))) {
        info += QString("工作集大小: %1 MB\n").arg(pmc.WorkingSetSize / 1024 / 1024);
        info += QString("峰值工作集: %1 MB\n").arg(pmc.PeakWorkingSetSize / 1024 / 1024);
        info += QString("页面文件使用: %1 MB\n").arg(pmc.PagefileUsage / 1024 / 1024);
        info += QString("峰值页面文件: %1 MB\n").arg(pmc.PeakPagefileUsage / 1024 / 1024);
        info += QString("私有使用: %1 MB\n").arg(pmc.PrivateUsage / 1024 / 1024);
    }
#endif

    return info;
}

QString CrashHandler::getModuleInfo(void* address)
{
#ifdef Q_OS_WIN
    HMODULE hModule;
    if (GetModuleHandleExA(GET_MODULE_HANDLE_EX_FLAG_FROM_ADDRESS,
                          static_cast<LPCSTR>(address),
                          &hModule)) {
        char moduleName[MAX_PATH];
        if (GetModuleFileNameA(hModule, moduleName, MAX_PATH)) {
            return QString::fromLocal8Bit(moduleName);
        }
    }
#else
    Q_UNUSED(address)
#endif
    return "Unknown";
}

QString CrashHandler::formatExceptionCode(DWORD code)
{
#ifdef Q_OS_WIN
    switch (code) {
    case EXCEPTION_ACCESS_VIOLATION:
        return "EXCEPTION_ACCESS_VIOLATION (访问违例)";
    case EXCEPTION_ARRAY_BOUNDS_EXCEEDED:
        return "EXCEPTION_ARRAY_BOUNDS_EXCEEDED (数组越界)";
    case EXCEPTION_BREAKPOINT:
        return "EXCEPTION_BREAKPOINT (断点)";
    case EXCEPTION_DATATYPE_MISALIGNMENT:
        return "EXCEPTION_DATATYPE_MISALIGNMENT (数据类型未对齐)";
    case EXCEPTION_FLT_DENORMAL_OPERAND:
        return "EXCEPTION_FLT_DENORMAL_OPERAND (浮点非正规操作数)";
    case EXCEPTION_FLT_DIVIDE_BY_ZERO:
        return "EXCEPTION_FLT_DIVIDE_BY_ZERO (浮点除零)";
    case EXCEPTION_FLT_INEXACT_RESULT:
        return "EXCEPTION_FLT_INEXACT_RESULT (浮点不精确结果)";
    case EXCEPTION_FLT_INVALID_OPERATION:
        return "EXCEPTION_FLT_INVALID_OPERATION (浮点无效操作)";
    case EXCEPTION_FLT_OVERFLOW:
        return "EXCEPTION_FLT_OVERFLOW (浮点溢出)";
    case EXCEPTION_FLT_STACK_CHECK:
        return "EXCEPTION_FLT_STACK_CHECK (浮点栈检查)";
    case EXCEPTION_FLT_UNDERFLOW:
        return "EXCEPTION_FLT_UNDERFLOW (浮点下溢)";
    case EXCEPTION_ILLEGAL_INSTRUCTION:
        return "EXCEPTION_ILLEGAL_INSTRUCTION (非法指令)";
    case EXCEPTION_IN_PAGE_ERROR:
        return "EXCEPTION_IN_PAGE_ERROR (页面错误)";
    case EXCEPTION_INT_DIVIDE_BY_ZERO:
        return "EXCEPTION_INT_DIVIDE_BY_ZERO (整数除零)";
    case EXCEPTION_INT_OVERFLOW:
        return "EXCEPTION_INT_OVERFLOW (整数溢出)";
    case EXCEPTION_INVALID_DISPOSITION:
        return "EXCEPTION_INVALID_DISPOSITION (无效处置)";
    case EXCEPTION_NONCONTINUABLE_EXCEPTION:
        return "EXCEPTION_NONCONTINUABLE_EXCEPTION (不可继续异常)";
    case EXCEPTION_PRIV_INSTRUCTION:
        return "EXCEPTION_PRIV_INSTRUCTION (特权指令)";
    case EXCEPTION_SINGLE_STEP:
        return "EXCEPTION_SINGLE_STEP (单步)";
    case EXCEPTION_STACK_OVERFLOW:
        return "EXCEPTION_STACK_OVERFLOW (栈溢出)";
    default:
        return QString("Unknown Exception (0x%1)").arg(code, 8, 16, QChar('0'));
    }
#else
    Q_UNUSED(code)
    return "Unknown";
#endif
}

void CrashHandler::cleanupOldDumps()
{
    qDebug() << "[CRASH_HANDLER] 清理旧的dump文件";

    QDir dumpDir(m_dumpDirectory);
    if (!dumpDir.exists()) {
        return;
    }

    // 获取所有dump文件
    QStringList filters;
    filters << "*.dmp" << "*.txt";
    QFileInfoList files = dumpDir.entryInfoList(filters, QDir::Files, QDir::Time);

    // 保留最新的10个文件，删除其余的
    const int maxFiles = 10;
    if (files.size() > maxFiles) {
        for (int i = maxFiles; i < files.size(); ++i) {
            QFile::remove(files[i].absoluteFilePath());
            qDebug() << "[CRASH_HANDLER] 删除旧文件:" << files[i].fileName();
        }
    }
}

bool CrashHandler::generateDump(const QString& reason)
{
    qDebug() << "[CRASH_HANDLER] 手动生成dump文件，原因:" << reason;

    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QString dumpFileName = QString("%1_manual_%2.dmp")
                          .arg(m_applicationName.isEmpty() ? "app" : m_applicationName)
                          .arg(timestamp);
    QString dumpFilePath = m_dumpDirectory + "/" + dumpFileName;

    return writeDumpFile(dumpFilePath, nullptr);
}

CrashInfo CrashHandler::getLastCrashInfo()
{
    return m_lastCrashInfo;
}

QString CrashHandler::collectThreadInfo()
{
    QString info;

#ifdef Q_OS_WIN
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPTHREAD, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        THREADENTRY32 te32;
        te32.dwSize = sizeof(THREADENTRY32);

        if (Thread32First(hSnapshot, &te32)) {
            DWORD currentProcessId = GetCurrentProcessId();
            info += "线程信息:\n";

            do {
                if (te32.th32OwnerProcessID == currentProcessId) {
                    info += QString("  线程ID: %1\n").arg(te32.th32ThreadID);
                }
            } while (Thread32Next(hSnapshot, &te32));
        }

        CloseHandle(hSnapshot);
    }
#endif

    return info;
}
