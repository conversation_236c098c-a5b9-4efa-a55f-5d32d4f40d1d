#ifndef DEBUG_CONFIG_H
#define DEBUG_CONFIG_H

#include <QDebug>
#include <QDateTime>

// 调试配置宏
#define DEBUG_CRASH_ENABLED 1

#if DEBUG_CRASH_ENABLED
    #define DEBUG_CRASH(msg) \
        qDebug() << "[DEBUG_CRASH]" << QDateTime::currentDateTime().toString("hh:mm:ss.zzz") \
                 << __FILE__ << ":" << __LINE__ << "-" << msg
    
    #define DEBUG_CRASH_FUNC_ENTER(func) \
        qDebug() << "[DEBUG_CRASH]" << QDateTime::currentDateTime().toString("hh:mm:ss.zzz") \
                 << "进入函数:" << func << "线程ID:" << QThread::currentThreadId()
    
    #define DEBUG_CRASH_FUNC_EXIT(func) \
        qDebug() << "[DEBUG_CRASH]" << QDateTime::currentDateTime().toString("hh:mm:ss.zzz") \
                 << "退出函数:" << func
    
    #define DEBUG_CRASH_VAR(var) \
        qDebug() << "[DEBUG_CRASH]" << QDateTime::currentDateTime().toString("hh:mm:ss.zzz") \
                 << #var << "=" << var
    
    #define DEBUG_CRASH_EXCEPTION(e) \
        qDebug() << "[DEBUG_CRASH]" << QDateTime::currentDateTime().toString("hh:mm:ss.zzz") \
                 << "异常:" << e.what() << "位置:" << __FILE__ << ":" << __LINE__
#else
    #define DEBUG_CRASH(msg)
    #define DEBUG_CRASH_FUNC_ENTER(func)
    #define DEBUG_CRASH_FUNC_EXIT(func)
    #define DEBUG_CRASH_VAR(var)
    #define DEBUG_CRASH_EXCEPTION(e)
#endif

// 前向声明
class CrashHandler;

// 崩溃分析辅助类
class CrashAnalyzer {
public:
    static void logSystemInfo();
    static void logMemoryUsage();
    static void logThreadInfo();
    static void setupCrashHandler();
};

#endif // DEBUG_CONFIG_H
