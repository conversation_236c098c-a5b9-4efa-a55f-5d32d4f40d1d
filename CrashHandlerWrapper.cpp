#include "CrashHandlerWrapper.h"
#include <QDebug>

CrashHandlerWrapper::CrashHandlerWrapper(QObject *parent)
    : QObject(parent)
{
}

void CrashHandlerWrapper::recordOperation(const QString& operation)
{
    qDebug() << "[CRASH_WRAPPER] 记录操作:" << operation;
    CrashHandler::recordLastOperation(operation);
}

bool CrashHandlerWrapper::generateDump(const QString& reason)
{
    qDebug() << "[CRASH_WRAPPER] 手动生成dump文件，原因:" << reason;
    return CrashHandler::generateDump(reason);
}

QString CrashHandlerWrapper::getLastCrashReport()
{
    CrashInfo info = CrashHandler::getLastCrashInfo();
    if (info.crashTime.isEmpty()) {
        return "无崩溃记录";
    }
    
    QString report = QString("最后崩溃信息:\n"
                           "时间: %1\n"
                           "异常: %2\n"
                           "地址: %3\n"
                           "模块: %4\n"
                           "最后操作: %5")
                    .arg(info.crashTime)
                    .arg(info.exceptionCode)
                    .arg(info.exceptionAddress)
                    .arg(info.moduleName)
                    .arg(info.lastOperation);
    
    return report;
}

void CrashHandlerWrapper::setAutoRestart(bool enable)
{
    qDebug() << "[CRASH_WRAPPER] 设置自动重启:" << enable;
    CrashHandler::setAutoRestart(enable);
}
