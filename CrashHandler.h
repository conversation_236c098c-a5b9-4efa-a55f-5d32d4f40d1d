#ifndef CRASHHANDLER_H
#define CRASHHANDLER_H

#include <QString>
#include <QDateTime>
#include <QDir>
#include <QCoreApplication>
#include <QDebug>
#include <QThread>
#include <QSysInfo>

#ifdef Q_OS_WIN
#include <windows.h>
#include <dbghelp.h>
#include <psapi.h>
#include <tlhelp32.h>
#pragma comment(lib, "dbghelp.lib")
#pragma comment(lib, "psapi.lib")
#endif

// 崩溃信息结构体
struct CrashInfo {
    QString crashTime;          // 崩溃时间
    QString applicationName;    // 应用程序名称
    QString applicationVersion; // 应用程序版本
    QString exceptionCode;      // 异常代码
    QString exceptionAddress;   // 异常地址
    QString moduleName;         // 崩溃模块名称
    QString threadId;           // 崩溃线程ID
    QString processId;          // 进程ID
    QString systemInfo;         // 系统信息
    QString memoryUsage;        // 内存使用情况
    QString lastOperation;      // 最后操作（从日志中提取）
    QString stackTrace;         // 堆栈跟踪
    QString dumpFilePath;       // dump文件路径
    QString reportFilePath;     // 报告文件路径
};

// 崩溃处理器类
class CrashHandler
{
public:
    // 安装崩溃处理器
    static bool install(const QString& dumpDir = "");
    
    // 卸载崩溃处理器
    static void uninstall();
    
    // 设置dump文件保存目录
    static void setDumpDirectory(const QString& dir);
    
    // 设置应用程序信息
    static void setApplicationInfo(const QString& name, const QString& version);
    
    // 手动生成dump文件（用于测试）
    static bool generateDump(const QString& reason = "Manual");
    
    // 获取最后的崩溃信息
    static CrashInfo getLastCrashInfo();
    
    // 设置是否在崩溃后自动重启
    static void setAutoRestart(bool enable);
    
    // 记录最后操作（用于崩溃分析）
    static void recordLastOperation(const QString& operation);

private:
    // Windows异常处理器
#ifdef Q_OS_WIN
    static LONG WINAPI unhandledExceptionFilter(EXCEPTION_POINTERS* exceptionInfo);
#endif
    
    // 生成dump文件
    static bool writeDumpFile(const QString& filePath, void* exceptionInfo = nullptr);
    
    // 生成崩溃报告
    static bool writeCrashReport(const CrashInfo& crashInfo);
    
    // 收集系统信息
    static QString collectSystemInfo();
    
    // 收集内存使用信息
    static QString collectMemoryInfo();
    
    // 收集线程信息
    static QString collectThreadInfo();
    
    // 获取模块信息
    static QString getModuleInfo(void* address);
    
    // 格式化异常代码
    static QString formatExceptionCode(DWORD code);
    
    // 清理旧的dump文件
    static void cleanupOldDumps();

private:
    static QString m_dumpDirectory;         // dump文件目录
    static QString m_applicationName;       // 应用程序名称
    static QString m_applicationVersion;    // 应用程序版本
    static bool m_autoRestart;              // 是否自动重启
    static QString m_lastOperation;         // 最后操作
    static CrashInfo m_lastCrashInfo;       // 最后崩溃信息
    
#ifdef Q_OS_WIN
    static LPTOP_LEVEL_EXCEPTION_FILTER m_previousFilter; // 之前的异常处理器
#endif
};

// 崩溃处理器安装宏（在main函数中使用）
#define INSTALL_CRASH_HANDLER(dumpDir) \
    do { \
        CrashHandler::setApplicationInfo(QCoreApplication::applicationName(), \
                                       QCoreApplication::applicationVersion()); \
        if (!CrashHandler::install(dumpDir)) { \
            qWarning() << "Failed to install crash handler"; \
        } else { \
            qDebug() << "Crash handler installed successfully"; \
        } \
    } while(0)

// 记录操作宏（在关键操作前使用）
#define RECORD_OPERATION(op) CrashHandler::recordLastOperation(op)

#endif // CRASHHANDLER_H
