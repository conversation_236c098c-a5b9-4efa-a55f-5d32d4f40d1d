﻿#include "monitor.h"
#include "ToolBox/csvedit.h"
#include "ToolBox/mathdiy.h"
#include "CommServ/control.h"
#include "CommServ/recvbuffer.h"
#include "CommServ/globalvariable.h"
#include "IDEProjectAndFile/projectandfilemanage.h"
#include <winsock2.h>
bool biglitte = 1;
QMap<QString, int> csvLine = {
    {"id", 0},
    {"settingName", 1},
    {"Scope", 2},
    {"Owned", 3},
    {"Type", 4},
    {"Name", 5},
    {"DataType", 6},
    {"dataTypeID", 7},
    {"ArrayLength", 8},
    {"Address", 9},
    {"isConstant", 10},
    {"isOpc", 11},
    {"IsRetained", 12},
    {"description", 13},
    {"createTime", 14},
    {"lastModifyTime", 15},
    {"varList", 16},
    {"isShow", 17},
    {"InitialValue", 18},
    {"isSelected", 19},
    {"state", 20},
    {"ModbusAddress", 21},
    {"ModbusRW", 22},
    {"segment", 23},
    {"offset", 24},
    {"ioxl", 25},
    {"MasterNo", 26},
    {"SlaveNo", 27},
    {"SlotNo", 28},
    {"IsRedundancy", 29},
    {"StructMainName", 30},
    {"StructParentName", 31},
    {"StructName", 32},
    {"StructDataType", 33},
    {"StructTotalBitLength", 34},
    {"StructParentBitOffset", 35},
    {"task_time", 36},
    {"program_priority", 37},
    {"st_priority", 38},
    {"space_len", 39},
    {"f_address", 40},
    {"data_type_group", 41},
    {"data_group", 42},
    {"type_logo", 42},
};

QMap<QString, int> csvLine_e{
    {"index", 0},
    {"data_type", 1},
    {"data_len", 2},
    {"segment", 3},
    {"bit", 4},
    {"offset", 5},
};

/*** temporary ***/

WatchTemp& WatchTemp::instance(){
    static WatchTemp instance;
    return instance;
}

void WatchTemp::init(){
    qDebug() << "[DEBUG_CRASH] WatchTemp::init()开始执行";
    qDebug() << "[DEBUG_CRASH] 当前线程ID:" << QThread::currentThreadId();

    try {
        QString CsvPath = ProjectAndFileManage::instance().getCurrentProjectPath() + "/TASK/all_data_flatten_st.csv";
        qDebug() << "[DEBUG_CRASH] CSV路径:" << CsvPath;
        qDebug() << "[DEBUG_CRASH] 文件存在检查:" << QFile::exists(CsvPath);

        qDebug() << "[DEBUG_CRASH] 清理数据前 - varlist大小:" << varlist.size();
        qDebug() << "[DEBUG_CRASH] 清理数据前 - name_index大小:" << name_index.size();

        varlist.clear();
        name_index.clear();

        qDebug() << "[DEBUG_CRASH] 开始读取CSV文件";
        readCSV(CsvPath, varlist, name_index);

        qDebug() << "[DEBUG_CRASH] CSV读取完成 - varlist大小:" << varlist.size();
        qDebug() << "[DEBUG_CRASH] CSV读取完成 - name_index大小:" << name_index.size();
        qDebug() << "[DEBUG_CRASH] WatchTemp::init()执行完成";

    } catch (const std::exception& e) {
        qDebug() << "[DEBUG_CRASH] WatchTemp::init()异常:" << e.what();
    } catch (...) {
        qDebug() << "[DEBUG_CRASH] WatchTemp::init()未知异常";
    }
}

QJsonArray WatchTemp::getVarTable(){
    QJsonArray arr;

    for(const auto& iter : qAsConst(varlist)){
        QJsonObject obj;
        obj.insert("Name", iter.at(csvLine_e["index"]));
        obj.insert("Type", iter.at(csvLine_e["data_type"]));
        obj.insert("Segment", iter.at(csvLine_e["segment"]));
        obj.insert("Offset", iter.at(csvLine_e["offset"]));
        obj.insert("Len", iter.at(csvLine_e["data_len"]));
        arr.append(obj);
    }

    return arr;
}

bool WatchTemp::addWatchVar(const QString& name, const QString& type){
    //qDebug() << "[INFO] addWatchVar" << varlist << name << type;
    for(const auto& iter : qAsConst(varlist)){
        if(iter.at(csvLine_e["index"]) == name && iter.at(csvLine_e["data_type"]) == type){
            qDebug() << "[INFO] addWatchVar" << iter.at(csvLine_e["index"])
                     << iter.at(csvLine_e["data_type"])
                     << allDataType.contains(iter.at(csvLine_e["data_type"]));
            if(!allDataType.contains(iter.at(csvLine_e["data_type"]))){
                return false;
            }
            monitorVars.append(iter);
            // qDebug() << "[INFO] addWatchVar" << iter.at(csvLine_e["index"]);
            return true;
        }
    }
    return false;
}

bool WatchTemp::delWatchVar(const QString& name, const QString& type){
    for(const auto& iter : qAsConst(monitorVars)){
        if(iter.at(csvLine_e["index"]) == name && iter.at(csvLine_e["data_type"]) == type){
            monitorVars.removeOne(iter);
            return true;
        }
    }
    return false;
}

void WatchTemp::clearWatch(){
    monitorVars.clear();
}

void WatchTemp::cfgWatchData(int start, int len){
    currentVars.clear();
    Watchtask.clear();

    if(!monitorVars.isEmpty()){
        //qDebug() << "[INFO] monitorVars" << monitorVars.first().size() << csvLine_e.size() << csvLine_e;
        if(monitorVars.first().size() != csvLine_e.size())
            return;
    }


    for(int i = start; i < monitorVars.size() && i < start + len; ++i){
        currentVars.append(monitorVars[i]);
    }

    std::sort(currentVars.begin(), currentVars.end(),[](const QStringList &a, const QStringList &b){
        return  a.at(csvLine_e["offset"]).toUInt() < b.at(csvLine_e["offset"]).toUInt();
    });
    qDebug() << "[INFO] currentVars" << currentVars.size() << currentVars;
    if(!currentVars.isEmpty()){
        QVector<unsigned int> seq; // 记录某段连续的包 seg
        unsigned int seg =currentVars.first().at(csvLine_e["segment"]).toUInt();
        seq.append(currentVars.first().at(csvLine_e["offset"]).toUInt());
        qDebug() << "[INFO] seg" << seq.first();
        for(int i = 1; i < currentVars.size(); ++ i){
            unsigned int forward = currentVars[i - 1].at(csvLine_e["offset"]).toUInt();
            unsigned int backend = currentVars[i].at(csvLine_e["offset"]).toUInt();
            unsigned int backlen = currentVars[i].at(csvLine_e["data_len"]).toUInt();
            qDebug() << "[INFO] backlen" << backlen;
            if(backend - forward + backlen > 50){
                unsigned int start =  seq.first();
                unsigned int len = forward - start + currentVars[i - 1].at(csvLine_e["data_len"]).toUInt();
                addPack(seg, start, len);
                //qDebug() << "[INFO] add len" << len << forward << start << currentVars[i].at(csvLine_e["data_len"]).toUInt() << currentVars[i].at(csvLine_e["data_len"]) << csvLine_e["data_len"];
                seq.clear();
                seq.append(backend);
            }
            else {
                seq.append(backend);
            }
        }
        // 记录最后段的包
        if(!seq.isEmpty()){
            unsigned int start =  seq.first();
            unsigned int len = currentVars.last().at(csvLine_e["offset"]).toUInt() - start + currentVars.last().at(csvLine_e["data_len"]).toUInt();
            addPack(seg, start, len);
            qDebug() << "[INFO] len" << len;
        }
    }
}

void WatchTemp::addPack(unsigned int seg, unsigned int off, unsigned int len){
    TEMP_PACK pack;
    memset(&pack, 0, sizeof(TEMP_PACK));
    pack.seg = seg;
    pack.offset = off;
    pack.len = len;
    Watchtask.append(pack);
}

void WatchTemp::sendWatchData(){
    qDebug() << "[INFO] sendWatchData";
    parseWatch();
    data.clear();
    data.reserve(Watchtask.size() * sizeof(TEMP_PACK) + sizeof(quint16));
    if(biglitte){
        for (TEMP_PACK& pack : Watchtask) {
            TEMP_PACK bigEndianPack = pack;
            bigEndianPack.seg = qToBigEndian(pack.seg);
            bigEndianPack.offset = qToBigEndian(pack.offset);
            bigEndianPack.len = qToBigEndian(pack.len);
            data.append(reinterpret_cast<const char *>(&bigEndianPack), sizeof(TEMP_PACK));
        }
        qDebug() << "[INFO] TEMP_PACK" << data.toHex(' ');
    }

    // for(const TEMP_PACK& Pag : Watchtask){
    //     data.append(reinterpret_cast<const char *>(&Pag), sizeof(TEMP_PACK));
    //     qDebug() << "[INFO] TEMP_PACK" << data.toHex(' ');
    // }

    quint16 crc = crc16(data);
    if(biglitte)
        crc = qToBigEndian(crc); // 转换为大端序
    //uint16_t crc16 = crc16_((uint8_t *)data.constData(), data.size());
    data.append(reinterpret_cast<const char*>(&crc), sizeof(crc));
    Control::sendMessage(data, 2);
}

void WatchTemp::startWatch(){
    if(!flag){
        flag = true;
        timer->start();
    }
}

void WatchTemp::stopWatch(){
    if(flag){
        flag = false;
        timer->stop();
    }
}

QJsonArray WatchTemp::parseWatch(){
    QJsonArray arr;
    QByteArray recvdata = recvbuffer.tempbuffer;
    qDebug() << "[INFO] parseWatch" << recvdata.toHex(' ');
    quint16 crc = crc16(recvdata.mid(0, recvdata.size() - 2));

    quint16 rawValue = qFromBigEndian<quint16>(reinterpret_cast<const uchar*>(recvdata.right(2).constData()));;
    //memcpy(&rawValue, recvdata.right(2).constData(), sizeof(quint16));
    qDebug() << "[INFO] parseWatch" << crc << rawValue;
    if(crc != rawValue)
        return arr;
    // qDebug() << "[INFO] crc"  << crc << qFromBigEndian<quint16>(&rawValue);

    recvdata = recvdata.mid(0, recvdata.size() - 2);
    messageData mesgData = messageData::parseData(recvdata);
    //qDebug() << "[INFO] parseWatch" << recvdata.toHex(' ') ;
    // 需要观测的变量

    for(const QStringList& iter : qAsConst(currentVars)){
        unsigned int off = iter.at(csvLine_e["offset"]).toUInt();
        unsigned int len = iter.at(csvLine_e["data_len"]).toUInt();
        QString name = iter.at(csvLine_e["index"]);
        QString type = iter.at(csvLine_e["data_type"]);
        for(const segdata& oter : mesgData.segData){
            qDebug() << "[INFO] off" << off << "oter.offset" << oter.tempPack.offset
                     << "off + len" << off + len << "oter.offset + oter.len" << oter.tempPack.offset + oter.tempPack.len;
            if(off >= oter.tempPack.offset && off + len <= oter.tempPack.offset + oter.tempPack.len){
                QByteArray vardata = oter.data;
                qDebug() << "[INFO] vardata" << vardata.toHex(' ') << off - oter.tempPack.offset << type << name;
                QString value;
                if(type != "BOOL")
                    // 数据部分是小端
                    value =  getDataFromInterProtocol(vardata, off - oter.tempPack.offset, type,  false);
                else{
                    QString bit = iter.at(csvLine_e["bit"]);
                    QByteArray varbool = vardata.mid(off - oter.tempPack.offset, 1);
                    value =  getDataFromInterProtocol(varbool, bit.toUInt(), type,  true);
                }

                QJsonObject obj;
                obj.insert("name", name);
                obj.insert("type", type);
                obj.insert("value", value);
                arr.append(obj);
                break;
            }
        }
    }
    qDebug() << "[INFO] parseWatch" << arr;
    return arr;
}

quint16 crc16(const QByteArray &data){
    quint16 crc = 0xFFFF;  // CRC初始化值
    for (int i = 0; i < data.size(); ++i) {
        crc ^= static_cast<quint8>(data[i]);  // 异或当前字节
        for (int j = 0; j < 8; ++j) {        // 处理8位
            if (crc & 0x0001) {               // 检查最低位是否为1
                crc >>= 1;                    // 右移1位
                crc ^= 0xA001;                // 异或多项式0xA001
            } else {
                crc >>= 1;                    // 右移1位
            }
        }
    }
    return crc;  // 返回最终CRC值
}

void tempTest(){
    WatchTemp& watch = WatchTemp::instance();
    watch.init();
    // watch.addWatchVar("EX2_H_DI3200A_DI_01", "BOOL");
    // watch.addWatchVar("EX2_H_DI3200A_DI_02", "BOOL");
    watch.addWatchVar("0", "UINT");
    watch.addWatchVar("1", "UINT");
    watch.addWatchVar("33", "INT");
    watch.cfgWatchData(0, 6);
    watch.startWatch();
}

uint16_t crc16_(uint8_t *buffer, uint16_t buffer_length){
    uint8_t crc_hi = 0xFF; /* high CRC byte initialized */
    uint8_t crc_lo = 0xFF; /* low CRC byte initialized */
    unsigned int i; /* will index into CRC lookup */

    /* pass through message buffer */
    while (buffer_length--) {
        i = crc_hi ^ *buffer++; /* calculate the CRC  */
        crc_hi = crc_lo ^ table_crc_hi[i];
        crc_lo = table_crc_lo[i];
    }

    return (crc_hi << 8 | crc_lo);
}
