﻿
/******************************************************************************
 * Copyright 2023-2024 Three Gorges Intelligent Industrial Control Technology Co., Ltd.
 * All right reserved. See COPYRIGHT for detailed Information.
 *
 * @file       ProjectTree.qml
 * @brief      项目树组件列表
 *
 * <AUTHOR>
 * @date       2024/02/20
 * @history
 *****************************************************************************/
import QtQuick 2.15
import QtQuick.Controls 2.15
import "qrc:/qml/control/menu"
import "qrc:/qml/skin/SkinElement"
import "qrc:/qml/skin"

//项目树组件列表
Rectangle {
    id: root
    property ExpandableListViewStyle skin: SkinManager.currentSkin.expandableListView
    //整体背景颜色
    property color bgColor: "lightgray"
    //整体边框颜色
    property color borderColor: skin.borderColor
    //整齐边框宽度
    property int borderWidth: skin.borderWidth
    //行高
    property int rowHeight: skin.rowHeight
    //字体大小
    property int fontSize: skin.fontSize
    //字体类型
    property string fontFamily: skin.fontFamily
    //标题偏移量
    property int titleOffset: skin.titleOffset
    //可展开节点字体颜色
    property color expendNodeTitleColor: skin.expendNodeTitleColor
    //可展开节点背景颜色
    property color expendNodeBackgroundColor: skin.expendNodeBackgroundColor
    //可选择节点字体颜色
    property color nodeTitleColor: skin.nodeTitleColor
    //可选择节点背景颜色
    property color nodeBackgroundColor: skin.nodeBackgroundColor
    //被选择的节点字体颜色
    property color selectedNodeTitleColor: skin.selectedNodeTitleColor
    //被选择的节点背景颜色
    property color selectedNodeBackgroundColor: skin.selectedNodeBackgroundColor
    //当前选择的子节点名称和ID
    property string selectedName: ""
    property int selectedId: 0
    //允许右键菜单项
    property bool enableRightKeyMenu: false
    //允许子节点点击
    property bool enableChildNodeClick: true
    //允许子节点双击
    property bool enableChildNodeDoubleClick: true
    //允许重复点击
    property bool enableReClcik: false
    //点击子项选择信号
    signal childNodeClicked(int nodeId, string nodeName)
    //双击子项信号
    signal childNodeDoubleClicked(int nodeId, string nodeName)
    //右键菜单点击信号
    signal rightMenuClicked(int nodeId, string nodeName, string keyName)
    //自定义的右键菜单
    property QkMenu contentMenu

    anchors.fill: parent
    anchors.top: parent.top
    anchors.topMargin: 0

    width: parent.width
    height: parent.height
    //增加背景颜色
    color: bgColor
    border.color: borderColor
    border.width: borderWidth

    MouseArea {
        anchors.top: listView.bottom
        anchors.fill: parent
        //propagateComposedEvents: true
        onClicked: {
            if (enableChildNodeClick) {
                //清空选择的子项
                clearSelectClick()
                // mouse.accepted = false
            }
        }
    }

    ListView {
        id: listView
        //anchors.fill: parent
        width: parent.width - 2
        //height: listView.contentHeight
        height: listView.contentHeight > parent.height ? parent.height : listView.contentHeight
        anchors.top: parent.top
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.topMargin: 5
        //边线
        spacing: 1
        boundsBehavior: Flickable.StopAtBounds
        clip: true
        snapMode: ListView.NoSnap
        model: ListModel {
            id: listModel
        }

        delegate: shownode_delegate

        Component {
            id: shownode_delegate
            Item {
                width: listView.width
                height: subNode.count > 0 ? expendNodeColumn.height : nodeRow.height
                //可展开节点---目录节点
                Column {
                    id: expendNodeColumn
                    visible: subNode.count > 0
                    //边线
                    spacing: 1
                    Component.onCompleted: {
                        //加载完毕后收缩所有的列表
                        for (var i = 1; i < expendNodeColumn.children.length - 1; ++i) {
                            expendNodeColumn.children[i].visible = model.extended
                        }
                    }
                    MouseArea {
                        width: listView.width
                        height: rowHeight
                        enabled: expendNodeColumn.children.length > 2
                        acceptedButtons: enableRightKeyMenu ? (Qt.LeftButton
                                                               | Qt.RightButton) : Qt.LeftButton
                        onClicked: {
                            if (mouse.button == Qt.LeftButton) {
                                var flag = false
                                model.extended = !model.extended
                                projectAndFileManage.setExtendedNode(
                                            model.Id, model.extended)
                                for (var i = 1; i < expendNodeColumn.children.length - 1; ++i) {
                                    flag = expendNodeColumn.children[i].visible
                                    expendNodeColumn.children[i].visible = model.extended
                                }
                                if (!flag) {
                                    iconAin.from = 0
                                    iconAin.to = 90
                                    iconAin.start()
                                } else {
                                    iconAin.from = 90
                                    iconAin.to = 0
                                    iconAin.start()
                                }

                                selectedName = model.EventName
                                selectedId = model.Id

                                //确定选中的模块
                                for (var k = 0; k < listModel.count; ++k) {
                                    var parentNode = listModel.get(k)
                                    selectNode(selectedId, parentNode)
                                }

                                if (mouseX >= group_name.x) {
                                    emit: childNodeClicked(model.Id,
                                                           model.EventName)
                                }
                            } else if (mouse.button == Qt.RightButton) {
                                if (model.haveMenu) {
                                    console.log("contentMenu.itemObj",
                                                model.Name, model.EventName)
                                    root.contentMenu.itemObj = model

                                    root.contentMenu.popup()
                                }
                            }
                        }

                        onDoubleClicked: {
                            if (enableChildNodeDoubleClick) {
                                emit: childNodeDoubleClicked(model.Id,
                                                             model.EventName)
                            }
                        }

                        Rectangle {
                            id: objItem
                            width: listView.width
                            height: parent.height
                            color: model.selected ? selectedNodeBackgroundColor : nodeBackgroundColor
                            Image {
                                id: expendIcon
                                width: 18
                                height: 18
                                anchors {
                                    verticalCenter: parent.verticalCenter
                                    left: parent.left
                                    leftMargin: titleOffset * model.level
                                }
                                source: "qrc:/assets/icon/arrow.png"
                                RotationAnimation {
                                    id: iconAin
                                    target: expendIcon
                                    duration: 150
                                }
                                Component.onCompleted: {
                                    for (var i = 1; i < expendNodeColumn.children.length - 1; ++i) {
                                        var flag = expendNodeColumn.children[i].visible
                                        if (flag) {
                                            iconAin.from = 0
                                            iconAin.to = 90
                                            iconAin.start()
                                            break
                                        }
                                    }
                                }
                            }

                            Image {
                                id: folderIcon
                                width: 20
                                height: 20
                                anchors {
                                    verticalCenter: parent.verticalCenter
                                    left: expendIcon.right
                                    leftMargin: 0
                                }
                                source: NodeIcon !== "" ? "qrc:/assets/icon/"
                                                          + NodeIcon : "qrc:/assets/icon/folder.png"
                            }
                            Label {
                                id: group_name
                                text: qsTr(Name) + (trans ? trans.transString : "")
                                anchors {
                                    verticalCenter: parent.verticalCenter
                                    left: folderIcon.right
                                    leftMargin: 5
                                }
                                font.pixelSize: root.fontSize
                                font.family: root.fontFamily
                                color: expendNodeTitleColor
                            }
                            Label {
                                text: qsTr(Description) + (trans ? trans.transString : "")
                                anchors {
                                    verticalCenter: parent.verticalCenter
                                    left: group_name.right
                                    leftMargin: titleOffset
                                }
                                font.pixelSize: root.fontSize - 2
                                font.family: root.fontFamily
                                color: expendNodeTitleColor
                            }
                        }
                    }
                    Repeater {
                        model: subNode
                        delegate: shownode_delegate
                    }
                }
                //一般节点---项目节点
                Rectangle {
                    id: nodeRow
                    visible: subNode.count === 0
                    color: model.selected ? selectedNodeBackgroundColor : nodeBackgroundColor
                    width: listView.width
                    height: rowHeight
                    MouseArea {
                        anchors.fill: parent
                        acceptedButtons: enableRightKeyMenu ? (Qt.LeftButton
                                                               | Qt.RightButton) : Qt.LeftButton
                        Row {
                            id: childRow
                            anchors.verticalCenter: parent.verticalCenter
                            leftPadding: (nodeRow.height - 10) + titleOffset * model.level
                            spacing: 5
                            Image {
                                id: lock
                                width: 15
                                height: 15
                                visible: model.nodeType === "file" &&
                                        projectAndFileManage.isEncryptedFile(
                                        projectAndFileManage.getFileInfoFromProjectTreeID(model.Id).id)
                                anchors.verticalCenter: parent.verticalCenter
                                source: "qrc:/assets/icon/slices/lock.png"
                            }
                            Image {
                                id: fileIcon
                                width: 20
                                height: 20
                                source: NodeIcon !== "" ? "qrc:/assets/icon/"
                                                          + NodeIcon : "qrc:/assets/icon/file.png"
                            }
                            Label {
                                text: model.Name
                                font.pixelSize: root.fontSize
                                font.family: root.fontFamily
                                color: model.selected ? selectedNodeTitleColor : nodeTitleColor
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            Label {
                                text: model.Description
                                font.pixelSize: root.fontSize
                                font.family: root.fontFamily
                                color: model.selected ? selectedNodeTitleColor : nodeTitleColor
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }

                        onClicked: {
                            console.debug("[DEBUG_CRASH] ProjectTree节点点击事件")
                            console.debug("[DEBUG_CRASH] 点击的节点ID:", model.Id, "名称:", model.EventName)
                            console.debug("[DEBUG_CRASH] 鼠标按键:", mouse.button)

                            if (mouse.button == Qt.LeftButton) {
                                if (enableChildNodeClick) {
                                    console.debug("[DEBUG_CRASH] 检查重复点击")
                                    console.debug("[DEBUG_CRASH] enableReClcik:", enableReClcik)
                                    console.debug("[DEBUG_CRASH] selectedName:", selectedName)
                                    console.debug("[DEBUG_CRASH] model.EventName:", model.EventName)

                                    //判断重复点击
                                    if (enableReClcik || selectedName !== model.EventName) {
                                        console.debug("[DEBUG_CRASH] 处理节点选择")
                                        selectedName = model.EventName
                                        selectedId = model.Id

                                        //确定选中的模块
                                        for (var k = 0; k < listModel.count; ++k) {
                                            var parentNode = listModel.get(k)
                                            selectNode(selectedId, parentNode)
                                        }

                                        console.debug("[DEBUG_CRASH] 发送childNodeClicked信号")
                                        emit: childNodeClicked(selectedId, selectedName)
                                    } else {
                                        console.debug("[DEBUG_CRASH] 重复点击，忽略")
                                    }
                                }
                            } else if (mouse.button == Qt.RightButton) {
                                console.debug("[DEBUG_CRASH] 右键点击处理")
                                if (model.haveMenu) {
                                    root.contentMenu.itemObj = model
                                    root.contentMenu.popup()
                                }
                            }
                        }

                        onDoubleClicked: {
                            lookEncryotFile(model.Id, model.EventName, lock.visible)

                            // if (enableChildNodeDoubleClick) {
                            //     emit: childNodeDoubleClicked(model.Id,
                            //                                  model.EventName)
                            // }
                        }
                    }
                }
            }
        }
    }

    //清除所有数据
    function clearData() {
        listModel.clear()
        selectedName = ""
        selectedId = 0
        emit: childNodeClicked(selectedId, selectedName)
    }

    //清除选中点击
    function clearSelectClick() {
        if (selectedName !== "") {
            selectedName = ""
            selectedId = 0
            for (var l = 0; l < listModel.count; ++l) {
                var parentNode = listModel.get(l)
                selectNode(selectedId, parentNode)
            }
            emit: childNodeClicked(selectedId, selectedName)
        }
    }

    // 设置节点选中
    function setSelectedNode(id, eventName)
    {
        clearSelectClick()
        selectedName = eventName
        selectedId = id

        //确定选中的模块
        for (var k = 0; k < listModel.count; ++k)
        {
            var parentNode = listModel.get(k)
            selectNode(selectedId, parentNode)
        }
    }

    //递归方式比较是否选中
    function selectNode(Id, node) {
        // console.log("Id ,node.Id,node.subNode.count", Id, node.Id,node.subNode.count)
        if (node.subNode.count > 0) {
            for (var i = 0; i < node.subNode.count; i++) {
                var childNode = node.subNode.get(i)
                selectNode(Id, childNode)
            }
        }
        if (String(node.Id) === String(Id)) {
            if (!node.selected) {
                node.selected = true
            }
        } else {
            if (node.selected) {
                node.selected = false
            }
        }
    }

    function addModelData(parentId, Id, nodeName, nodeType, nodeDescription, nodeIcon, eventName, haveMenu, extended) {
        //查找父节点
        var index = findNodeIndex(parentId)

        if (index === null) {
            //根节点
            listModel.append({
                                 "Id": Id,
                                 "Name": nodeName,
                                 "nodeType": nodeType,
                                 "Description": nodeDescription,
                                 "NodeIcon": nodeIcon,
                                 "EventName": eventName,
                                 "haveMenu": haveMenu,
                                 "level": 1,
                                 "selected": false,
                                 "extended": extended,
                                 "subNode": []
                             })
        } else {
            index.subNode.append({
                                     "Id": Id,
                                     "Name": nodeName,
                                     "nodeType": nodeType,
                                     "Description": nodeDescription
                                                    === "" ? "" : "(" + nodeDescription + ")",
                                     "NodeIcon": nodeIcon,
                                     "EventName": eventName,
                                     "haveMenu": haveMenu,
                                     "level": index.level + 1,
                                     "selected": false,
                                     "extended": extended,
                                     "subNode": []
                                 })
        }
    }

    function findNodeIndex(parentId) {
        if (parentId !== 0) {
            for (var i = 0; i < listModel.count; ++i) {
                var node = listModel.get(i)
                var findnode = findNodeById(parentId, node)
                if (findnode !== null) {
                    return findnode
                }
            }
        }
        return null
    }

    function findNodeById(parentId, node) {
        if (node.Id === parentId) {
            return node
        } else {
            for (var i = 0; i < node.subNode.count; ++i) {
                var findnode = findNodeById(parentId, node.subNode.get(i))
                if (findnode !== null) {
                    return findnode
                }
            }
        }
        return null
    }
}
