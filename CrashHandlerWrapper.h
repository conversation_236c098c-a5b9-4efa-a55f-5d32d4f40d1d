#ifndef CRASHHANDLERWRAPPER_H
#define CRASHHANDLERWRAPPER_H

#include <QObject>
#include <QString>
#include "CrashHandler.h"

// QML包装类，用于在QML中调用崩溃处理器功能
class CrashHandlerWrapper : public QObject
{
    Q_OBJECT
    
public:
    explicit CrashHandlerWrapper(QObject *parent = nullptr);
    
    // 记录操作（供QML调用）
    Q_INVOKABLE void recordOperation(const QString& operation);
    
    // 手动生成dump文件（供QML调用）
    Q_INVOKABLE bool generateDump(const QString& reason = "Manual");
    
    // 获取最后崩溃信息（供QML调用）
    Q_INVOKABLE QString getLastCrashReport();
    
    // 设置自动重启
    Q_INVOKABLE void setAutoRestart(bool enable);
};

#endif // CRASHHANDLERWRAPPER_H
