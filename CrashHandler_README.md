# 崩溃处理器使用说明

## 概述

本项目已集成了完整的崩溃处理系统，能够在应用程序发生崩溃时自动生成dump文件和详细的崩溃报告，帮助开发者快速定位和解决问题。

## 功能特性

### 1. 自动崩溃检测
- 捕获所有未处理的Windows异常
- 支持多种异常类型：访问违例、栈溢出、除零错误等
- 在崩溃发生时自动触发处理流程

### 2. Dump文件生成
- 生成标准的Windows MiniDump文件
- 包含进程内存、线程信息、调用栈等详细数据
- 可使用Visual Studio或WinDbg进行分析

### 3. 崩溃报告
- 生成可读的文本格式崩溃报告
- 包含系统信息、内存使用、最后操作等上下文
- 便于快速了解崩溃情况

### 4. 操作记录
- 记录关键操作的执行轨迹
- 在崩溃报告中显示最后执行的操作
- 帮助定位崩溃触发点

## 文件说明

### 核心文件
- `CrashHandler.h/cpp` - 崩溃处理器核心实现
- `CrashHandlerWrapper.h/cpp` - QML接口包装类
- `debug_config.h` - 调试配置和宏定义

### 生成文件
- `*.dmp` - 二进制dump文件，用于详细分析
- `*_crash_*.txt` - 文本格式崩溃报告
- 默认保存位置：`应用程序目录/CrashDumps/`

## 使用方法

### 1. 在C++中记录操作
```cpp
#include "CrashHandler.h"

// 记录关键操作
CrashHandler::recordLastOperation("正在执行关键操作X");

// 手动生成dump文件（用于测试）
CrashHandler::generateDump("测试原因");
```

### 2. 在QML中记录操作
```qml
// 记录操作
crashHandler.recordOperation("用户点击了按钮X")

// 手动生成dump
crashHandler.generateDump("手动测试")

// 获取最后崩溃信息
var lastCrash = crashHandler.getLastCrashReport()
```

### 3. 测试崩溃处理器
在QML中调用 `testCrashHandler()` 函数可以测试dump文件生成功能。

## 分析崩溃

### 1. 查看崩溃报告
打开生成的 `.txt` 文件，查看：
- 崩溃时间和异常类型
- 最后执行的操作
- 系统和内存信息

### 2. 分析Dump文件
使用Visual Studio：
1. 打开Visual Studio
2. 文件 -> 打开 -> 文件，选择 `.dmp` 文件
3. 点击"使用混合模式调试"
4. 查看调用栈、变量值等详细信息

使用WinDbg：
1. 安装Windows SDK中的WinDbg
2. 打开dump文件
3. 使用命令分析：`!analyze -v`

## 配置选项

### 自动重启
```cpp
CrashHandler::setAutoRestart(true);  // 启用崩溃后自动重启
```

### 自定义dump目录
```cpp
CrashHandler::setDumpDirectory("D:/MyApp/Crashes");
```

### 应用程序信息
```cpp
CrashHandler::setApplicationInfo("MyApp", "1.0.0");
```

## 注意事项

1. **性能影响**：崩溃处理器对正常运行性能影响极小
2. **文件清理**：系统会自动保留最新的10个dump文件
3. **权限要求**：需要在dump目录有写入权限
4. **平台支持**：目前仅支持Windows平台

## 故障排除

### 问题：无法生成dump文件
- 检查目录权限
- 确认磁盘空间充足
- 查看控制台日志输出

### 问题：崩溃处理器未触发
- 确认已正确安装：查看启动日志
- 检查是否被其他异常处理器覆盖

### 问题：dump文件过大
- 可以修改dump类型，减少包含的信息
- 在 `CrashHandler.cpp` 中调整 `MINIDUMP_TYPE` 设置

## 开发建议

1. 在关键操作前后添加操作记录
2. 定期测试崩溃处理器功能
3. 建立崩溃报告收集和分析流程
4. 根据崩溃报告优化代码稳定性
